{"name": "express-simple-starter", "version": "1.0.0", "description": "Simple Express.js starter template", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["express", "nodejs", "api"], "author": "InulCode", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}}