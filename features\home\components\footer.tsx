import Link from "next/link";
import { IconBrandGithubFilled as <PERSON>con<PERSON><PERSON><PERSON>, IconBrandTwitterFilled as <PERSON>con<PERSON>witter } from "@tabler/icons-react";

export function Footer() {
  const socialLinks = [
    {
      href: "#",
      icon: (
        <IconGithub className="w-5 h-5 text-zinc-500 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-zinc-100 transition-colors" />
      ),
    },
    {
      href: "#",
      icon: (
        <IconTwitter className="w-5 h-5 text-zinc-500 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-zinc-100 transition-colors" />
      ),
    },
  ];

  return (
    <footer className="border-t border-zinc-200 dark:border-zinc-800">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 py-8 flex flex-col items-center space-y-6 text-center">
        {/* Social Links */}
        <div className="flex gap-4">
          {socialLinks.map((link, index) => (
            <Link
              key={index}
              href={link.href || "#"}
              target="_blank"
              rel="noopener noreferrer"
            >
              {link.icon}
            </Link>
          ))}
        </div>

        {/* Copyright Notice */}
        <p className="text-sm text-zinc-500 dark:text-zinc-400">
          &copy; {new Date().getFullYear()} InulCode. All rights reserved.
        </p>
      </div>
    </footer>
  );
}
